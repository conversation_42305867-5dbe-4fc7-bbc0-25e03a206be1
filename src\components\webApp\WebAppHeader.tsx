import { memo } from "react";
import logo from "@/assets/adeptos_logo.png";

interface WebAppHeaderProps {
  currentPhase: number;
  totalPhases: number;
  onBack: () => void;
  canGoBack: boolean;
}

export const WebAppHeader = memo(function WebAppHeader({
  currentPhase,
  totalPhases,
  onBack,
  canGoBack,
}: WebAppHeaderProps) {
  return (
    <header className="fixed top-0 left-0 right-0 z-40 backdrop-blur-lg border-gray/20 dark:border-light/20 theme-transition">
      <div className="relative flex items-center px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
        {/* Back Button - Left Side */}
        <div className="flex items-center w-16 sm:w-20 md:w-24">
          {canGoBack ? (
            <button
              onClick={onBack}
              className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gray/10 dark:bg-light/10 hover:bg-gray/20 dark:hover:bg-light/20 theme-transition group"
              aria-label="Go back to previous phase"
            >
              <svg
                className="w-5 h-5 sm:w-6 sm:h-6 text-dark dark:text-light theme-transition group-hover:scale-110 transition-transform duration-200"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
          ) : null}
        </div>
        <div className="absolute left-1/2 top-1/2 transform -translate-x-1/2 -translate-y-1/2 flex items-center">
          <img
            src={logo}
            alt="Adeptos AI Logo"
            className="w-12 h-12 sm:w-16 sm:h-16 md:w-20 md:h-20 drop-shadow-sm hover:scale-105 transition-transform duration-200"
          />
        </div>

        <div className="flex items-center space-x-2 sm:space-x-3 ml-auto w-16 sm:w-20 md:w-24 justify-end">
          <div className="text-sm sm:text-base font-body text-dark dark:text-white theme-transition">
            <span className="font-semibold">{currentPhase + 1}</span>
            <span className="text-gray dark:text-gray mx-1">/</span>
            <span className="text-gray dark:text-gray">{totalPhases}</span>
          </div>
          <div className="w-16 sm:w-20 md:w-24 h-2 bg-gray/20 dark:bg-light/20 rounded-full overflow-hidden">
            <div
              className="h-full bg-dark dark:bg-light theme-transition transition-all duration-500 ease-out"
              style={{
                width: `${((currentPhase + 1) / totalPhases) * 100}%`,
              }}
            />
          </div>
        </div>
      </div>
    </header>
  );
});
