import { memo } from "react";
import logo from "@/assets/adeptos_logo.png";

interface WebAppHeaderProps {
  currentPhase: number;
  totalPhases: number;
  onBack: () => void;
  canGoBack: boolean;
}

export const WebAppHeader = memo(function WebAppHeader({
  currentPhase,
  totalPhases,
  onBack,
  canGoBack,
}: WebAppHeaderProps) {
  return (
    <header className="fixed top-0 left-0 right-0 z-50 bg-white/80 dark:bg-dark/80 backdrop-blur-md border-b border-gray/20 dark:border-light/20 theme-transition">
      <div className="flex items-center justify-between px-4 sm:px-6 lg:px-8 py-3 sm:py-4">
        {/* Back Button */}
        <div className="flex items-center">
          {canGoBack ? (
            <button
              onClick={onBack}
              className="flex items-center justify-center w-10 h-10 sm:w-12 sm:h-12 rounded-full bg-gray/10 dark:bg-light/10 hover:bg-gray/20 dark:hover:bg-light/20 theme-transition group"
              aria-label="Go back to previous phase"
            >
              <svg
                className="w-5 h-5 sm:w-6 sm:h-6 text-dark dark:text-light theme-transition group-hover:scale-110 transition-transform duration-200"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M15 19l-7-7 7-7"
                />
              </svg>
            </button>
          ) : (
            <div className="w-10 h-10 sm:w-12 sm:h-12" />
          )}
        </div>

        {/* Logo */}
        <div className="flex items-center">
          <img
            src={logo}
            alt="Adeptos AI Logo"
            className="w-8 h-8 sm:w-10 sm:h-10 md:w-12 md:h-12 drop-shadow-sm hover:scale-105 transition-transform duration-200"
          />
          <span className="ml-2 sm:ml-3 text-lg sm:text-xl md:text-2xl font-brand font-bold text-dark dark:text-light theme-transition">
            adeptos
            <span className="text-gray dark:text-gray">.ai</span>
          </span>
        </div>

        {/* Phase Progress */}
        <div className="flex items-center space-x-2 sm:space-x-3">
          {/* Phase Counter */}
          <div className="text-sm sm:text-base font-body text-dark dark:text-light theme-transition">
            <span className="font-semibold">{currentPhase + 1}</span>
            <span className="text-gray dark:text-gray mx-1">/</span>
            <span className="text-gray dark:text-gray">{totalPhases}</span>
          </div>

          {/* Progress Bar */}
          <div className="w-16 sm:w-20 md:w-24 h-2 bg-gray/20 dark:bg-light/20 rounded-full overflow-hidden">
            <div
              className="h-full bg-dark dark:bg-light theme-transition transition-all duration-500 ease-out"
              style={{
                width: `${((currentPhase + 1) / totalPhases) * 100}%`,
              }}
            />
          </div>
        </div>
      </div>

      {/* Phase Title (Optional) */}
      <div className="px-4 sm:px-6 lg:px-8 pb-2 sm:pb-3">
        <div className="text-center">
          <h1 className="text-base sm:text-lg md:text-xl font-title font-semibold text-dark dark:text-light theme-transition">
            {getPhaseTitle(currentPhase)}
          </h1>
        </div>
      </div>
    </header>
  );
});

// Helper function to get phase titles
function getPhaseTitle(phase: number): string {
  const phaseTitles = [
    "Información Básica",
    "Detalles del Negocio", 
    "Objetivos y Metas",
    "Configuración Técnica",
    "Revisión Final"
  ];
  
  return phaseTitles[phase] || `Fase ${phase + 1}`;
}
