import { Phase0 } from "@/components/webApp/phases";
import { useState } from "react";

export function WebApp() {
  const [phase, setPhase] = useState(0);
  setPhase(0)
  return (
    <div className="text-dark dark:text-light p-8">
      {
        (() => {
          switch (phase) {
            case 0:
              return <Phase0 />;
            default:
              return null;
          }
        })()
      }
    </div>
  );
}
