import { Phase0, WebAppHeader } from "@/components/webApp/phases";
import { useState } from "react";

export function WebApp() {
  const [phase, setPhase] = useState(0);
  const [formData, setFormData] = useState({});

  const totalPhases = 5; // Ajusta según el número total de fases que planees

  const handleBack = () => {
    if (phase > 0) {
      setPhase(phase - 1);
    }
  };

  const handleNext = (data?: Record<string, unknown>) => {
    if (data) {
      setFormData((prev) => ({ ...prev, ...data }));
    }
    if (phase < totalPhases - 1) {
      setPhase(phase + 1);
    }
  };

  const renderPhase = () => {
    switch (phase) {
      case 0:
        return <Phase0 onNext={handleNext} formData={formData} />;
      default:
        return <div className=""></div>;
    }
  };

  return (
    <div className="relative min-h-screen overflow-x-hidden theme-transition">
      {/* Header */}
      <WebAppHeader
        currentPhase={phase}
        totalPhases={totalPhases}
        onBack={handleBack}
        canGoBack={phase > 0}
      />

      {/* Main Content */}
      <main className="relative z-10 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">{renderPhase()}</div>
      </main>
    </div>
  );
}
