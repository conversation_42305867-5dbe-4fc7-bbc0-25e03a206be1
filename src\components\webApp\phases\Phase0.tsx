interface Phase0Props {
  onNext: (data?: Record<string, unknown>) => void;
  formData: Record<string, unknown>;
}

export function Phase0({ onNext, formData }: Phase0Props) {
  return (
    <div className="min-h-[60vh] flex items-center justify-center">
      <div className="text-center space-y-6">
        <h2 className="text-2xl sm:text-3xl md:text-4xl font-title font-bold text-dark dark:text-light theme-transition">
          Bienvenido a Adeptos AI
        </h2>
        <p className="text-lg sm:text-xl text-gray dark:text-gray theme-transition max-w-2xl">
          Vamos a configurar tu experiencia personalizada paso a paso
        </p>
        <button
          onClick={() => onNext()}
          className="px-8 py-3 bg-dark dark:bg-light text-white dark:text-dark rounded-lg font-semibold hover:bg-gray dark:hover:bg-gray theme-transition transform hover:scale-105 transition-all duration-200"
        >
          Comenzar
        </button>
      </div>
    </div>
  );
}
